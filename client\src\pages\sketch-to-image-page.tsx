/**
 * Temporary simplified version of SketchToImagePage to fix compilation errors
 */

import React from 'react';
import DashboardLayout from '@/components/layout/dashboard-layout';
export default function SketchToImagePage() {
  return (
    <DashboardLayout pageTitle="Boceto a Imagen">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Boceto a Imagen
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Esta herramienta está temporalmente en mantenimiento.
            </p>
            <p className="text-gray-500">
              Estamos trabajando para mejorar la experiencia. Vuelve pronto.
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
                      {/* Separador */}
                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-background px-2 text-muted-foreground">
                            O dibujar boceto
                          </span>
                        </div>
                      </div>

                      {/* Canvas de dibujo */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Dibujar boceto</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={clearCanvas}
                            className="gap-2"
                          >
                            <Trash2 className="h-3 w-3" />
                            Limpiar
                          </Button>
                        </div>
                        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 bg-muted/10">
                          <canvas
                            ref={canvasRef}
                            width={300}
                            height={200}
                            style={canvasStyles}
                            onMouseDown={handleMouseDown}
                            onMouseMove={handleMouseMove}
                            onMouseUp={handleMouseUp}
                            onMouseLeave={handleMouseLeave}
                            onTouchStart={handleTouchStart}
                            onTouchMove={handleTouchMove}
                            onTouchEnd={handleTouchEnd}
                            className="w-full rounded-md"
                          />
                        </div>
                        <p className="text-xs text-muted-foreground text-center">
                          Dibuja directamente en el canvas
                        </p>
                      </div>
                      {/* Prompt */}
                      <div className="space-y-2">
                        <Label htmlFor="prompt">Descripción (requerido)</Label>
                        <Textarea
                          id="prompt"
                          placeholder="Describe la imagen que quieres generar..."
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          rows={3}
                          className="resize-none"
                        />
                      </div>

                      {/* Prompt negativo */}
                      <div className="space-y-2">
                        <Label htmlFor="negative-prompt">Prompt Negativo (opcional)</Label>
                        <Textarea
                          id="negative-prompt"
                          placeholder="Describe lo que NO quieres en la imagen..."
                          value={negativePrompt}
                          onChange={(e) => setNegativePrompt(e.target.value)}
                          rows={2}
                          className="resize-none"
                        />
                      </div>

                      {/* Control Strength */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label>Fuerza del Control</Label>
                          <Badge variant="outline">{controlStrength.toFixed(1)}</Badge>
                        </div>
                        <Slider
                          value={[controlStrength]}
                          min={0.1}
                          max={1}
                          step={0.1}
                          onValueChange={(value) => setControlStrength(value[0])}
                          className="w-full"
                        />
                        <p className="text-xs text-muted-foreground">
                          Valores bajos: más libertad creativa • Valores altos: sigue fielmente el boceto
                        </p>
                      </div>

                      {/* Estilo y formato */}
                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                          <Label>Estilo</Label>
                          <Select value={stylePreset} onValueChange={setStylePreset}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {stylePresets.map((style) => (
                                <SelectItem key={style.value} value={style.value}>
                                  {style.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Formato</Label>
                          <Select value={outputFormat} onValueChange={setOutputFormat}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {outputFormats.map((format) => (
                                <SelectItem key={format.value} value={format.value}>
                                  {format.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Botón de generar */}
                      <Button
                        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                        onClick={generateImage}
                        disabled={!imageFile || !prompt || isGenerating}
                        size="lg"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Generando imagen...
                          </>
                        ) : (
                          <>
                            <Wand2 className="mr-2 h-4 w-4" />
                            Generar Imagen
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Área de Visualización */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="lg:col-span-2"
                >

                  {resultImage ? (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Sparkles className="h-5 w-5 text-purple-600" />
                          Imagen Generada
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Imagen resultado */}
                        <div className="flex justify-center">
                          <div className="relative">
                            <img
                              src={resultImage}
                              alt="Imagen generada"
                              className="w-full max-w-lg rounded-lg border shadow-lg"
                            />
                          </div>
                        </div>

                        {/* Botones de acción */}
                        <div className="flex flex-wrap gap-3 justify-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyToClipboard}
                            className="flex-1 min-w-[120px]"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copiar
                          </Button>

                          <Button
                            onClick={handleToggleFavorite}
                            className={`flex-1 min-w-[120px] ${
                              currentSketchSaved
                                ? "bg-red-500 hover:bg-red-600 text-white"
                                : "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                            }`}
                          >
                            <Heart className={`w-4 h-4 mr-2 ${currentSketchSaved ? "fill-current" : ""}`} />
                            {currentSketchSaved ? "Quitar" : "Guardar"}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={downloadResult}
                            className="flex-1 min-w-[120px]"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Descargar
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                          <PenTool className="h-12 w-12 text-purple-600" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          Convierte tu boceto en imagen
                        </h3>
                        <p className="text-gray-600 mb-4 max-w-md">
                          Sube un boceto o dibuja directamente, añade una descripción y genera una imagen realista con IA.
                        </p>
                        <div className="flex items-center gap-2 text-sm text-purple-600">
                          <Sparkles className="h-4 w-4" />
                          <span>Resultados en segundos</span>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </motion.div>
              </motion.div>
            </TabsContent>

            <TabsContent value="saved" className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5 text-primary" />
                    Bocetos Guardados
                    <Badge variant="secondary">{savedSketches.length}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {savedSketches.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {savedSketches.map((savedSketch) => (
                        <motion.div
                          key={savedSketch.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                        >
                          {/* Imagen procesada */}
                          <div className="relative aspect-square">
                            <img
                              src={savedSketch.processedUrl}
                              alt="Boceto guardado"
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute top-2 right-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const filteredSketches = savedSketches.filter(sketch => sketch.id !== savedSketch.id);
                                  setSavedSketches(filteredSketches);

                                  toast({
                                    title: "💔 Eliminada",
                                    description: "Boceto eliminado de favoritos.",
                                  });
                                }}
                                className="bg-white/90 hover:bg-white"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Información */}
                          <div className="p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="secondary" className="text-xs">
                                {savedSketch.outputFormat.toUpperCase()}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {new Date(savedSketch.timestamp).toLocaleDateString()}
                              </span>
                            </div>

                            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                              {savedSketch.prompt}
                            </p>

                            <p className="text-xs text-muted-foreground mb-3 truncate">
                              {savedSketch.originalFilename}
                            </p>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // Cargar el boceto en la galería principal
                                  setImageFile(null);
                                  setImageSrc(savedSketch.originalUrl);
                                  setResultImage(savedSketch.processedUrl);
                                  setOriginalName(savedSketch.originalFilename);
                                  setPrompt(savedSketch.prompt);
                                  setNegativePrompt(savedSketch.negativePrompt || "");
                                  setControlStrength(savedSketch.controlStrength);
                                  setStylePreset(savedSketch.stylePreset || "enhance");
                                  setOutputFormat(savedSketch.outputFormat);

                                  // Cambiar a la pestaña "Última Generación"
                                  setActiveTab("latest");

                                  toast({
                                    title: "🖼️ Boceto cargado",
                                    description: "Boceto cargado en la galería principal.",
                                  });
                                }}
                                className="flex-1"
                              >
                                <RotateCcw className="h-3 w-3 mr-1" />
                                Cargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const link = document.createElement("a");
                                  link.href = savedSketch.processedUrl;
                                  link.download = `sketch_${savedSketch.originalFilename}`;
                                  link.click();
                                }}
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-600 mb-2">
                        No hay bocetos guardados
                      </h3>
                      <p className="text-gray-500">
                        Los bocetos que guardes aparecerán aquí
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
