/**
 * Twitter/X Ad Editor - Specialized editor for Twitter/X Ads
 */

import { GenericAdEditor } from "@/components/tools/ad-creator/shared/GenericAdEditor";
import { twitterConfig } from "@/config/platform-configs";

export default function TwitterAdEditor() {
  return (
    <GenericAdEditor
      config={twitterConfig}
      platformKey="twitter"
    />
  );
}

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header compacto con información de plataforma */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-gradient-to-r ${twitterConfig.color} text-white py-4 px-4 md:px-6`}
      >
        <div className="max-w-[1600px] mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Button>

              <div className="flex items-center gap-3">
                <div className="p-2 bg-white/20 backdrop-blur-sm rounded-lg">
                  <Twitter className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">
                    {twitterConfig.name}
                  </h1>
                  <p className="text-sm text-white/80">
                    {twitterConfig.description}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge className="bg-white/20 text-white border-white/30 text-xs">
                <Target className="w-3 h-3 mr-1" />
                {twitterConfig.specifications.aspectRatio}
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30 text-xs">
                <Camera className="w-3 h-3 mr-1" />
                {twitterConfig.specifications.fileSize}
              </Badge>
              <Badge className="bg-white/20 text-white border-white/30 text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                Emma AI
              </Badge>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Layout principal con diseño convencional */}
      <div className="max-w-[1600px] mx-auto p-4 md:p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Columna izquierda - Controles */}
          <div className="lg:col-span-1 space-y-6">
            {/* Controles principales */}
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Wand2 className="h-5 w-5" />
                  Crear Anuncio Twitter/X
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Prompt input */}
                <div className="space-y-2">
                  <Label htmlFor="prompt">Describe tu anuncio</Label>
                  <Textarea
                    id="prompt"
                    placeholder="Ej: Anuncio viral para Twitter con diseño moderno y llamativo..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[100px] resize-none"
                  />
                </div>

                {/* Size selector */}
                <div className="space-y-2">
                  <Label>Tamaño del anuncio</Label>
                  <div className="grid grid-cols-1 gap-2">
                    {twitterConfig.sizes.map((sizeOption) => (
                      <Button
                        key={sizeOption}
                        variant={size === sizeOption ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSize(sizeOption)}
                        className="justify-start"
                      >
                        {sizeOption}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Product images toggle */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="use-product-images">Usar imágenes de producto</Label>
                    <Switch
                      id="use-product-images"
                      checked={useProductImages}
                      onCheckedChange={setUseProductImages}
                    />
                  </div>

                  {useProductImages && (
                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => productInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Subir imágenes de producto
                      </Button>
                      <input
                        ref={productInputRef}
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleProductImageUpload(e.target.files)}
                        className="hidden"
                      />

                      {productImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {productImages.map((image, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(image)}
                                alt={`Producto ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeProductImage(index)}
                                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Generate button */}
                <Button
                  onClick={handleGenerateAd}
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generando...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generar Anuncio
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Especificaciones */}
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Target className="h-5 w-5" />
                  Especificaciones de {twitterConfig.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="font-medium text-gray-700">Aspecto:</span>
                    <br />
                    <span className="text-[#3018ef] font-semibold">{twitterConfig.specifications.aspectRatio}</span>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="font-medium text-gray-700">Tamaño:</span>
                    <br />
                    <span className="text-[#3018ef] font-semibold">{twitterConfig.specifications.fileSize}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <span className="font-medium text-gray-700 text-sm">Formatos:</span>
                  <div className="flex gap-2">
                    {twitterConfig.specifications.fileTypes.map((type) => (
                      <Badge key={type} variant="secondary" className="text-xs">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <span className="font-medium text-gray-700 text-sm">Mejores prácticas:</span>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {twitterConfig.specifications.guidelines.map((guideline, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-[#3018ef] mt-1">•</span>
                        {guideline}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Columna central - Preview */}
          <div className="lg:col-span-2">
            <Card className="h-fit">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Camera className="h-5 w-5" />
                  Vista Previa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="latest">Último Generado</TabsTrigger>
                    <TabsTrigger value="saved">Guardados ({savedAds.length})</TabsTrigger>
                  </TabsList>

                  <TabsContent value="latest" className="mt-4">
                    {currentAd ? (
                      <div className="space-y-4">
                        <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                          <img
                            src={currentAd.image_url}
                            alt="Anuncio generado"
                            className="w-full h-auto"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSaveAd(currentAd)}
                            disabled={isAdSaved(currentAd.image_url)}
                          >
                            <Heart className="w-4 h-4 mr-2" />
                            {isAdSaved(currentAd.image_url) ? "Guardado" : "Guardar"}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const link = document.createElement('a');
                              link.href = currentAd.image_url;
                              link.download = `twitter-ad-${Date.now()}.png`;
                              link.click();
                            }}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Descargar
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <Camera className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>Tu anuncio aparecerá aquí una vez generado</p>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="saved" className="mt-4">
                    {savedAds.length > 0 ? (
                      <div className="grid grid-cols-2 gap-4">
                        {savedAds.map((ad) => (
                          <div key={ad.id} className="relative group">
                            <img
                              src={ad.image_url}
                              alt="Anuncio guardado"
                              className="w-full h-auto rounded-lg"
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                              <Button
                                variant="secondary"
                                size="sm"
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = ad.image_url;
                                  link.download = `twitter-ad-${ad.id}.png`;
                                  link.click();
                                }}
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => removeAd(ad.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        <Heart className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No tienes anuncios guardados aún</p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Emma AI Assistant */}
      <EmmaAdAssistant
        platform="twitter"
        currentPrompt={prompt}
        onPromptSuggestion={(suggestedPrompt) => {
          setPrompt(suggestedPrompt);
          toast({
            title: "Prompt sugerido por Emma",
            description: "He actualizado tu descripción con una sugerencia optimizada",
          });
        }}
        onSizeRecommendation={(recommendedSize) => {
          setSize(recommendedSize);
          toast({
            title: "Tamaño recomendado por Emma",
            description: `Tamaño actualizado a ${recommendedSize}`,
          });
        }}
        className="right-6"
      />
    </div>
  );
}
